<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>UEST</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>googlesignin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>uest</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need access to save photos to your media library.</string>
  <key>NSCameraUsageDescription</key>
  <string>We need access to your camera to scan and take photos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your media library to allow photo selection.</string>
  <key>NSAppleMusicUsageDescription</key>
  <string>This app does not use Apple Music, but needs access due to underlying libraries.</string>
	<key>UIAppFonts</key>
	<array>
		<string>FontAwesome.ttf</string>
		<string>Feather.ttf</string>
		<string>Ionicons.ttf</string>
		<string>AncizarSerif-Bold.ttf</string>
		<string>AncizarSerif-BoldItalic.ttf</string>
		<string>AncizarSerif-Italic-VariableFont_wght.ttf</string>
		<string>AncizarSerif-Regular.ttf</string>
		<string>AncizarSerif-VariableFont_wght.ttf</string>
    <string>MaterialIcons.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
