import { Dimensions } from "react-native";

export const FontFamily = {};
export const { height, width } = Dimensions.get('window');

export const PrimaryColors = {
  BLACK: '#000000',
  ORANGE: '#F66500',
  WHITE: '#FFFFFF',
  ERROR: '#ed3124',
  INPUTFILEDBORDER: '#FFFFFF26',
  SELECTEDTABCOLOR: 'orange',
  ORANGEFORTOGGLE: '#FF914D',
  CARDBORDER: '#FF914D',
  LIGHTGRAY: '#1a1919',
  LIGHTGRAY2: '#f2f2f7',
  GRAYSHADOW: '#737272',
  CARDBACKGROUNDDARK: '#0c0300',
  LIGHT_HEADER_BG: '#F9F9F9',
  LIGHT_BORDER: '#E0E0E0',
  LIGHT_ITEM_BORDER: '#E8E8E8',
  DARK_HEADER_BG: '#2C2C2C',
  DARK_BORDER: '#3A3A3A',
  DARK_ITEM_BG: '#242424',
  DARK_ITEM_BORDER: '#333333',
  RED: '#FF0000',
  YELLOW: '#FFFF00',
  GREEN: '#00FF00',
  BLUE: '#0000FF',
  WARNINGTEXTBROWN: '#8A6D3B',
  WARNINGNBACKGROUNDYELLOW: '#FFF3CD',
  WARNINGICON: '#a94442',
  LIGHTORANGE: '#faf7f5',
  DARKORANGE: '#f0d7c7',
};

export const getThemeColors = (isDarkMode: boolean) => ({
  background: isDarkMode ? '#000000' : '#FFFFFF',
  card: isDarkMode ? '#1A1A1A' : '#FFFFFF',
  text: isDarkMode ? '#FFFFFF' : '#000000',
  muted: isDarkMode ? '#888888' : '#666666',
  border: isDarkMode ? '#333333' : '#E5E5E5',
  accent: '#FD904B',
  accentLight: '#FFF4EF',
  accentMedium: '#FFE4D1',
  accentDark: '#E8803D',
  greyLight: isDarkMode ? '#2A2A2A' : '#F8F8F8',
  greyMedium: isDarkMode ? '#404040' : '#E8E8E8',
  greyDark: isDarkMode ? '#666666' : '#CCCCCC',
  firstPlace: '#FD904B',
  secondPlace: isDarkMode ? '#555555' : '#888888',
  thirdPlace: isDarkMode ? '#444444' : '#AAAAAA',
  streakHigh: '#FD904B',
  streakMedium: '#FFB366',
  streakLow: '#FFCC99',
  surface: isDarkMode ? '#1A1A1A' : '#FAFAFA',
  surfaceSecondary: isDarkMode ? '#2A2A2A' : '#F0F0F0',
  shadow: isDarkMode ? '#000000' : '#00000010',
});

export const IMAGE_CONSTANT = {
  FINDCLASSES: require('../Assets/Images/FindTutionCLasses.png'),
  EXAMLOGO: require('../Assets/Images/exam-time.png'),
  ABOUT: require('../Assets/Images/About.png'),
  DESCRIPTION: require('../Assets/Images/edit-info.png'),
  GRADUATION: require('../Assets/Images/Graduation.png'),
  EXPERIENCE: require('../Assets/Images/brifcase.png'),
  MEDAL: require('../Assets/Images/medal.png'),
  PHOTOFRAME: require('../Assets/Images/PhotoFrame.png'),
  TUITIONCLASS: require('../Assets/Images/TuitionClass.png'),
  DELETE: require('../Assets/Images/delete.png'),
  SMWATERPARKLOGO: require('../Assets/Images/sm_water_park.png'),
  NALANDALOGO: require('../Assets/Images/nalanda.png'),
  UESTCOIN: require('../Assets/Images/uest_coin.png'),
  GOOGLEICON: require('../Assets/Images/GoogleIcon.png'),
  REFERAL: require('../Assets/Images/refer.png'),
  EDUCATION: require('../Assets/Images/education.png'),
  ARTANDCRAFT: require('../Assets/Images/art_craft.png'),
  DRAMA: require('../Assets/Images/drama.png'),
  MUSIC: require('../Assets/Images/music.png'),
  SPORTS: require('../Assets/Images/sports.png'),
  LANGUAGE: require('../Assets/Images/foreignlan.png'),
  TECHNOLOGY: require('../Assets/Images/technology.png'),
  ART: require('../Assets/Images/art.png'),
  SHIVWATERPARK: require('../Assets/Images/shiv_water_park.png'),

  LOGINSTUDNET: require('../Assets/Images/studentlogin.png'),
  HEART: require('../Assets/Images/heart.png'),
  COOKING: require('../Assets/Images/cooking_class.png'),
  DANCE: require('../Assets/Images/dance.png'),
  COMPUTER: require('../Assets/Images/computer_class.png'),
  VAIDIKMATHS: require('../Assets/Images/Vaidik_Maths.png'),
  GYMNASTIC: require('../Assets/Images/gymnastic.png'),
  YOGA: require('../Assets/Images/yoga.png'),
  AVIATION: require('../Assets/Images/aviation.png'),
  HEARTBLACK: require('../Assets/Images/heartblack.png'),
  HEARTWHITE: require('../Assets/Images/heartwhite.png'),


  DESIGNING: require('../Assets/Images/designing.png'),
  GARBA: require('../Assets/Images/garba.png'),
  USEAPPASILLUS: require('../Assets/Images/illus2.png'),
  STAR:require('../Assets/Images/star.png'),
  SCHOOL:require('../Assets/Images/school_logo.png'),
  STUDENT:require('../Assets/Images/student.png'),
  FILTTER:require('../Assets/Images/filter.png'),
  UESTLOGO: require('../Assets/Images/UestLogo.png'),
  UWHIZLOGO: require('../Assets/Images/U-Whiz-Exam.png'),
  FIRSTPRIZE: require('../Assets/Images/First_prize.png'),
  STUDENTICON: require('../Assets/Images/student_icon.png'),
  SUBJECT: require('../Assets/Images/subject.png'),
  MESSAGE:require('../Assets/Images/envelopes.png'),
  REVIEW:require('../Assets/Images/feedback-alt.png'),
  CLASSLISTREVIEW:require('../Assets/Images/review.png'),
  CLASSLISTRATING:require('../Assets/Images/star_rating.png'),
  CLASSEXPIRENCE:require('../Assets/Images/experience_medal.png'),
  NEWUESTCOIN: require('../Assets/Images/newUestCoin.png'),
  CELLULARWORLD: require('../Assets/Images/cellular_world.png'),
  RBNEWS: require('../Assets/Images/rb-news.png'),
  COMMENT:require('../Assets/Images/comment.png'),
  FEEDBACK:require('../Assets/Images/feedback.png'),
  RANNDASS:require('../Assets/Images/rann-dass.png'),
  ATTENDENCE:require('../Assets/Images/attendance.png'),
  CLASSWORK:require('../Assets/Images/presentation.png'),
  HOMEWORK:require('../Assets/Images/homework.png'),
  LEAVES:require('../Assets/Images/absentism.png'),
  TIMETABLE:require('../Assets/Images/schedule.png'),
  EXAMCARD:require('../Assets/Images/exam.png'),
  FEES:require('../Assets/Images/payment.png'),
  CALENDAR:require('../Assets/Images/calendar.png'),

  BADGESTREAK:require('../Assets/Badges/streak.png'),
  BADGEMONTH:require('../Assets/Badges/month.png'),
  BADGEYEAR:require('../Assets/Badges/year.png'),
  BADGE100:require('../Assets/Badges/100.png'),
  BADGE1000:require('../Assets/Badges/1000.png'),
  BADGE10000:require('../Assets/Badges/10000.png'),

  DAILYQUIZ: require('../Assets/Images/dailyquiz.png'),
  PROFILE: require('../Assets/Images/profile.png'),
  SEARCHCLASSES: require('../Assets/Images/findclasses.png'),
  ANNOUCEMENTS: require('../Assets/Images/announcement.png'),
  DOCUMENTS: require('../Assets/Images/documentation.png'),
  STORE: require('../Assets/Images/payment.png'),
  CLASSMATE: require('../Assets/Images/classmate-rm.png'),
  RUBIKCUBE: require('../Assets/Images/rubikcube-rm.png'),
};
