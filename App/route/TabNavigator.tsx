/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSelector } from 'react-redux';

import { RootState } from '../Redux/store';
import Home from '../Screens/Student/Home/Home';
import Setting from '../Screens/Student/Setting/Setting';
import strings from '../Utils/LocalizedStrings/LocalizedStrings';
import ClassList from '../Screens/Student/ClassList/ClassList';
import ERPStack from './ERPStack';
import DailyQuiz from '../Screens/Student/DailyQuiz/DailyQuiz';
import Store from '../Screens/Student/Store/Store';
import { checkStudentIsInClass } from '../services/studentClassesService';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
  const styles = isDarkMode ? darkStyles : lightStyles;
  const [showMyClassTab, setShowMyClassTab] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  useEffect(() => {
    const fetchClassStatus = async () => {
      try {
        const response = await checkStudentIsInClass();
        if (response?.data) {
          setShowMyClassTab(true);
        }
      } catch (e) {
        console.log('Error fetching class status', e.response);
      } finally {
        setLoading(false);
      }
    };
    fetchClassStatus();
  }, []);

  if (loading) { 
    return null
  };


  const tabItems = [
    { name: strings.Home.HOME, component: Home, icon: 'home' },
    { name: strings.Home.DAILYQUIZ, component: DailyQuiz, icon: 'bulb' },
    { name: 'Store', component: Store, icon: 'storefront' },
    { name: strings.Home.SEARCH, component: ClassList, icon: 'search' },
    ...(showMyClassTab
      ? [{ name: 'My Class', component: ERPStack, icon: 'business' }]
      : []),
    { name: strings.Home.SETTING, component: Setting, icon: 'settings' },
  ];

  return (
    <Tab.Navigator
      screenOptions={({ route }) => {
        const tabItem = tabItems.find((item) => item.name === route.name);
        const iconName = tabItem?.icon || 'circle';

        return {
          headerShown: false,
          tabBarStyle: styles.tabBar,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: isDarkMode ? '#FFFFFF' : '#000000',
          tabBarInactiveTintColor: '#DBCDCD',
          tabBarIcon: ({ focused, color, size }) => (
            <>
              {focused && <View style={styles.headercolor} />}
              <Ionicons
                name={focused ? iconName : `${iconName}-outline`}
                size={size}
                color={color}
              />
            </>
          ),
        };
      }}
    >
      {tabItems.map((item, index) => (
        <Tab.Screen
          key={index}
          name={item.name}
          component={item.component}
          options={{ tabBarLabel: item.name }}
        />
      ))}
    </Tab.Navigator>
  );
};

export default TabNavigator;

// Light mode styles
const lightStyles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#ffffff',
    height: Platform.OS === 'android' ? 60 : 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 10,
  },
  headercolor: {
    borderWidth: 1,
    width: '40%',
    marginBottom: 8,
    marginTop: 8,
    height: 1,
    backgroundColor: '#000000',
  },
});

// Dark mode styles
const darkStyles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#000000',
    height: Platform.OS === 'android' ? 60 : 80,
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 10,
  },
  headercolor: {
    borderWidth: 1,
    width: '40%',
    marginBottom: 8,
    marginTop: 8,
    height: 1,
    backgroundColor: '#FFFFFF',
  },
});
