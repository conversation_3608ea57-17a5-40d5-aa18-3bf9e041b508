import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Attendance from '../Screens/ERP/Attendance';
import { ClassWorkScreen } from '../Screens/ERP/Classwork';
import { HomeWorkScreen } from '../Screens/ERP/Homework';
import Leave from '../Screens/ERP/Leave';
import { TimetableScreen } from '../Screens/ERP/Timetable';
import { DisciplineScreen } from '../Screens/ERP/DisciplineScreen';
import Dashboard from '../Screens/ERP/Dashboard';
import AnnualCalendarScreen from '../Screens/ERP/AnnualCalendarScreen';
import { AnnoucementsScreen } from '../Screens/ERP/Annoucements';
import { DocumentsScreen } from '../Screens/ERP/Documents';

export type ERPStackParamList = {
  ERPHome: undefined;
};

const Stack = createNativeStackNavigator<ERPStackParamList>();

const ERPStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Home"
        component={Dashboard}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Attendance"
        component={Attendance}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Classwork"
        component={ClassWorkScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Homework"
        component={HomeWorkScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Leave"
        component={Leave}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Timetable"
        component={TimetableScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Discipline"
        component={DisciplineScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Annoucements"
        component={AnnoucementsScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AnnualCalender"
        component={AnnualCalendarScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Documents"
        component={DocumentsScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default ERPStack;
