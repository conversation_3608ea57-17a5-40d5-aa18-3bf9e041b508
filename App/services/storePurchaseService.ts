import axiosInstance from '../config/axios';

export interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
  image?: string;
}

export interface PurchaseData {
  cartItems: CartItem[];
  totalCoins: number;
}

export interface PurchaseResponse {
  success: boolean;
  orderId: string;
  orderIds: string[];
  message: string;
}

export interface StoreOrder {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string | null;
  itemId: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    description: string;
    coinPrice: number;
    quantity: number;
    category: string;
    image: string | null;
    status: string;
  };
}

export async function purchaseItems(data: PurchaseData) {
  try {
    const response = await axiosInstance.post('/store/purchase', data);
    return {
      success: true,
      data: response.data.data as PurchaseResponse
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Purchase failed'
    };
  }
}

export async function getMyOrders() {
  try {
    const response = await axiosInstance.get('/store/orders');
    return {
      success: true,
      data: response.data.data as StoreOrder[]
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch orders'
    };
  }
}

export async function getOrderDetails(orderId: string) {
  try {
    const response = await axiosInstance.get(`/store/orders/${orderId}`);
    return {
      success: true,
      data: response.data.data as StoreOrder
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch order details'
    };
  }
}

export function validateCartItems(cartItems: CartItem[]): { isValid: boolean; error?: string } {
  if (!cartItems || cartItems.length === 0) {
    return { isValid: false, error: 'Cart is empty' };
  }

  for (const item of cartItems) {
    if (!item.id || !item.name || !item.coinPrice || !item.quantity) {
      return { isValid: false, error: 'Invalid cart item structure' };
    }

    if (item.quantity <= 0) {
      return { isValid: false, error: `Invalid quantity for ${item.name}` };
    }

    if (item.coinPrice <= 0) {
      return { isValid: false, error: `Invalid price for ${item.name}` };
    }
  }

  return { isValid: true };
}


export function calculateTotalCoins(cartItems: CartItem[]): number {
  return cartItems.reduce((total, item) => {
    return total + (item.coinPrice * item.quantity);
  }, 0);
}


export function validatePurchaseData(data: PurchaseData): { isValid: boolean; error?: string } {
  const cartValidation = validateCartItems(data.cartItems);
  if (!cartValidation.isValid) {
    return cartValidation;
  }

  const calculatedTotal = calculateTotalCoins(data.cartItems);
  if (data.totalCoins !== calculatedTotal) {
    return { 
      isValid: false, 
      error: `Total coins mismatch. Expected: ${calculatedTotal}, Provided: ${data.totalCoins}` 
    };
  }

  if (data.totalCoins <= 0) {
    return { isValid: false, error: 'Invalid total coins amount' };
  }

  return { isValid: true };
}


export async function processPurchase(data: PurchaseData) {
  // Validate purchase data first
  const validation = validatePurchaseData(data);
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error
    };
  }

  // Proceed with purchase
  return await purchaseItems(data);
}
