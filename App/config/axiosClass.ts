import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ClassBaseUrl } from './apiUrl';

const axiosClass = axios.create({
  baseURL: ClassBaseUrl + '/api/student',
});

axiosClass.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

export default axiosClass;