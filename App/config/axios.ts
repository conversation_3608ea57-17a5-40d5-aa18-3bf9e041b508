import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiUrl, ExamBaseUrl } from './apiUrl';

// You can add more environment/config logic here if needed
const baseURL = apiUrl;
const baseURL2 = ExamBaseUrl;

export const axiosInstance = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  // withCredentials is not used in React Native
});

// Request interceptor
axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // Dynamic baseURL switching
    const serverSelect = config.headers && (config.headers as any)['Server-Select'];
    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;

    // Attach token from AsyncStorage if available
    const studentToken = await AsyncStorage.getItem('token');
    console.log(studentToken)
    if (studentToken) {
      if (typeof config.headers.set === 'function') {
        config.headers.set('Authorization', `Bearer ${studentToken}`);
      }
    }
    console.log(config)
    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    if (error.response && error.response.status === 401) {
      // Optionally: Show a toast, clear AsyncStorage, navigate to login, etc.
      // Example:
      // import { ToastAndroid } from 'react-native';
      // ToastAndroid.show('Unauthorized', ToastAndroid.LONG);
      AsyncStorage.removeItem('token');
      AsyncStorage.removeItem('user');
      // Optionally navigate to login screen here
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;