import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import { getLeaderBoard } from '../../../services/dailyQuizService';
import { getThemeColors } from '../../../Utils/Constants';

interface LeaderboardUser {
    rank: number;
    studentId: string;
    score: number;
    coinEarnings: number;
    streakCount: number;
    firstName: string;
    lastName: string;
    email: string;
}

const LIMIT = 10;

export default function Leaderboard() {
    const { isDarkMode } = IndexStyle();
    const [filter, setFilter] = useState<'today' | 'weekly' | 'all-time'>('today');
    const [leaderboardData, setLeaderboardData] = useState<LeaderboardUser[]>([]);
    const [page, setPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);

    const themeColors = getThemeColors(isDarkMode);

    const fetchLeaderboard = async (pg: number, reset = false) => {
        if (isLoading) { return; }
        if (!reset && !hasMore) { return; }

        setIsLoading(true);

        try {
            const res = await getLeaderBoard(pg, LIMIT, filter);
            const fetched = res.data || [];

            setLeaderboardData(prev =>
                reset ? fetched : [...prev, ...fetched]
            );
            setHasMore(fetched.length === LIMIT);
            setPage(pg + 1);
        } catch (error) {
            // Handle error silently
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        const loadData = async () => {
            setPage(1);
            setHasMore(true);
            setLeaderboardData([]);
            await fetchLeaderboard(1, true);
        };

        loadData();
    }, [filter]);

    const getInitials = (firstName: string, lastName: string) =>
        `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();

    const renderInitialCircle = (firstName: string, lastName: string, size = 40, rank?: number) => {
        let backgroundColor = themeColors.greyLight;
        let textColor = themeColors.text;

        if (rank === 1) {
            backgroundColor = themeColors.firstPlace;
            textColor = '#FFFFFF';
        } else {
            backgroundColor = isDarkMode ? '#2A2A2A' : '#F0F0F0';
            textColor = isDarkMode ? '#CCCCCC' : '#333333';
        }

        return (
            <View
                style={{
                    width: size,
                    height: size,
                    borderRadius: size / 2,
                    backgroundColor: backgroundColor,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginHorizontal: 10,
                    shadowColor: '#000',
                    shadowOffset: {width: 0, height: 2},
                    shadowOpacity: 0.2,
                    shadowRadius: 4,
                    elevation: 4,
                }}>
                <Text style={{ fontSize: size / 2.5, fontWeight: '700', color: textColor }}>
                    {getInitials(firstName, lastName)}
                </Text>
            </View>
        );
    };

    const getStreakColor = (streakCount: number) => {
        if (streakCount >= 14) { return themeColors.accent; }
        if (streakCount >= 7) { return themeColors.text; }
        if (streakCount >= 3) { return themeColors.muted; }
        return themeColors.greyDark;
    };

    const getStreakBackgroundColor = (streakCount: number) => {
        if (streakCount >= 14) { return themeColors.accent + '20'; }
        if (streakCount >= 7) { return themeColors.text + '15'; }
        if (streakCount >= 3) { return themeColors.muted + '15'; }
        return themeColors.greyLight;
    };



    const renderItem = ({ item }: { item: LeaderboardUser }) => {
        if (item.rank === 1) { return null; }
        return (
        <View
            key={item.studentId}
            style={[
                styles.listItem,
                {
                    backgroundColor: themeColors.card,
                    borderColor: item.rank === 1 ? themeColors.accent + '30' :
                               themeColors.border,
                    shadowColor: themeColors.shadow,
                },
            ]}>

            {/* Left Section: Rank + Avatar */}
            <View style={styles.leftSection}>
                <View style={[styles.rankBadgeList, {
                    backgroundColor: themeColors.greyMedium,
                }]}>
                    <Text style={[styles.rankBadgeText, {
                        color: themeColors.text,
                        fontWeight: '600',
                    }]}>
                        {item.rank}
                    </Text>
                </View>
                {renderInitialCircle(item.firstName, item.lastName, 50, item.rank)}
            </View>

            {/* Middle Section: User Info */}
            <View style={styles.middleSection}>
                <Text style={[styles.listUserName, { color: themeColors.text }]}>
                    {item.firstName} {item.lastName}
                </Text>

                {/* Stats Row */}
                <View style={styles.statsRow}>
                    {/* Score */}
                    <View style={[styles.statItem, { backgroundColor: themeColors.greyLight }]}>
                        <MaterialIcons name="quiz" size={12} color={themeColors.text} />
                        <Text style={[styles.statText, { color: themeColors.text }]}>
                            {item.score}
                        </Text>
                    </View>

                    {/* Streak */}
                    <View style={[styles.statItem, {
                        backgroundColor: getStreakBackgroundColor(item.streakCount),
                        borderWidth: item.streakCount >= 14 ? 1 : 0,
                        borderColor: item.streakCount >= 14 ? themeColors.accent + '40' : 'transparent',
                    }]}>
                        <MaterialIcons name="local-fire-department" size={12} color={getStreakColor(item.streakCount)} />
                        <Text style={[styles.statText, {
                            color: getStreakColor(item.streakCount),
                            fontWeight: item.streakCount >= 14 ? '700' : '600',
                        }]}>
                            {item.streakCount}d
                        </Text>
                    </View>
                </View>
            </View>

            {/* Right Section: Coins */}
            <View style={styles.rightSection}>
                <View style={[styles.coinsBadgeList, {
                    backgroundColor: themeColors.greyLight,
                    shadowColor: '#000000',
                    shadowOffset: {width: 0, height: 1},
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                    elevation: 2,
                }]}>
                    {/* Custom UEST Coin Icon */}
                    <View style={styles.uestCoinIcon}>
                        <Text style={styles.uestCoinText}>U</Text>
                    </View>
                    <Text style={[styles.coinsTextList, {
                        color: themeColors.text,
                        fontWeight: '600',
                    }]}>{item.coinEarnings}</Text>
                </View>
            </View>
        </View>
        );
    };

    const winner = leaderboardData.find(user => user.rank === 1);

    return (
        <SafeAreaProvider>
            <NavigationHeader title="Leaderboard" onBackPress={() => { }} />
            <SafeAreaView
                style={[styles.container, {backgroundColor: themeColors.background}]}
                edges={['left', 'right']}>

                {/* Header */}
                <View style={[styles.headerSection, {backgroundColor: themeColors.card}]}>
                    <View style={styles.headerContent}>
                        <MaterialIcons name="leaderboard" size={32} color={themeColors.text} />
                        <Text style={[styles.headerTitle, {color: themeColors.text}]}>
                            Quiz Champions
                        </Text>
                        <Text style={[styles.headerSubtitle, {color: themeColors.muted}]}>
                            Compete with fellow learners
                        </Text>
                    </View>
                </View>

                <View style={styles.filterRow}>
                    {['today', 'weekly', 'all-time'].map(key => (
                        <TouchableOpacity
                            key={key}
                            onPress={() => setFilter(key as any)}
                            style={[
                                styles.filterButton,
                                {
                                    backgroundColor: filter === key ? themeColors.accent : themeColors.card,
                                    borderColor: filter === key ? themeColors.accent : themeColors.border,
                                },
                            ]}>
                            <Text
                                style={{
                                    color: filter === key ? '#FFFFFF' : themeColors.text,
                                    fontWeight: '600',
                                    fontSize: 14,
                                }}>
                                {key === 'today' ? 'Today' : key === 'weekly' ? 'This Week' : 'All Time'}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>

                <FlatList
                    ListHeaderComponent={
                        <>
                            {leaderboardData.length > 0 ? (
                                <>
                                    {/* Winner Spotlight - Dream11 Style */}
                                    {winner && (
                                        <View style={[styles.winnerSpotlight, {
                                            backgroundColor: themeColors.card,
                                            borderColor: themeColors.border,
                                            borderWidth: 1,
                                        }]}>
                                            {/* Crown and Trophy */}
                                            <View style={styles.winnerCrownContainer}>
                                                <MaterialIcons name="emoji-events" size={32} color={themeColors.greyDark} />
                                            </View>

                                            {/* Winner Avatar */}
                                            <View style={styles.winnerAvatarContainer}>
                                                {renderInitialCircle(winner.firstName, winner.lastName, 100, 1)}
                                                <View style={[styles.winnerRankBadge, { backgroundColor: themeColors.firstPlace }]}>
                                                    <Text style={[styles.winnerRankText, {color: '#FFFFFF'}]}>1</Text>
                                                </View>
                                            </View>

                                            {/* Winner Info */}
                                            <Text style={[styles.winnerName, { color: themeColors.text }]}>
                                                {winner.firstName} {winner.lastName}
                                            </Text>
    

                                            {/* Winner Stats */}
                                            <View style={styles.winnerStatsContainer}>
                                                {/* Score */}
                                                <View style={[styles.winnerStatItem, { backgroundColor: themeColors.greyLight }]}>
                                                    <MaterialIcons name="quiz" size={18} color={themeColors.text} />
                                                    <Text style={[styles.winnerStatText, { color: themeColors.text }]}>
                                                        {winner.score}
                                                    </Text>
                                                </View>

                                                {/* Streak */}
                                                <View style={[styles.winnerStatItem, {
                                                    backgroundColor: getStreakBackgroundColor(winner.streakCount),
                                                    borderWidth: winner.streakCount >= 14 ? 1 : 0,
                                                    borderColor: winner.streakCount >= 14 ? themeColors.accent + '40' : 'transparent',
                                                }]}>
                                                    <MaterialIcons name="local-fire-department" size={18} color={getStreakColor(winner.streakCount)} />
                                                    <Text style={[styles.winnerStatText, {
                                                        color: getStreakColor(winner.streakCount),
                                                        fontWeight: winner.streakCount >= 14 ? '800' : '700',
                                                    }]}>
                                                        {winner.streakCount} days
                                                    </Text>
                                                </View>

                                                {/* Coins */}
                                                <View style={[styles.winnerStatItem, { backgroundColor: themeColors.greyLight }]}>
                                                    {/* Custom UEST Coin Icon */}
                                                    <View style={styles.uestCoinIconLarge}>
                                                        <Text style={styles.uestCoinTextLarge}>U</Text>
                                                    </View>
                                                    <Text style={[styles.winnerStatText, { color: themeColors.text }]}>
                                                        {winner.coinEarnings}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    )}

                                    {/* Section Title */}
                                    <View style={styles.sectionTitleContainer}>
                                        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                                            All Participants
                                        </Text>
                                        <View style={[styles.sectionTitleLine, { backgroundColor: themeColors.border }]} />
                                    </View>
                                </>
                            ) : (
                                <Text style={[styles.title, { color: themeColors.text }]}>
                                    No data available for {filter}
                                </Text>
                            )}
                        </>
                    }
                    data={leaderboardData}
                    renderItem={renderItem}
                    keyExtractor={item => item.studentId}
                    onEndReached={() => fetchLeaderboard(page)}
                    onEndReachedThreshold={0.3}
                    ListFooterComponent={
                        isLoading ? (
                            <View style={{ paddingVertical: 20 }}>
                                <ActivityIndicator size="large" color={themeColors.accent} />
                            </View>
                        ) : null
                    }
                    contentContainerStyle={{ paddingBottom: 80 }}
                />
            </SafeAreaView>
        </SafeAreaProvider>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

    // Header Section
    headerSection: {
        paddingHorizontal: 20,
        paddingVertical: 24,
        marginBottom: 16,
    },
    headerContent: {
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: '800',
        marginTop: 8,
        marginBottom: 4,
    },
    headerSubtitle: {
        fontSize: 14,
        fontWeight: '500',
    },

    title: {
        fontSize: 20,
        fontWeight: '700',
        marginVertical: 16,
        paddingHorizontal: 16,
    },
    filterRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginBottom: 20,
        paddingHorizontal: 16,
    },
    filterButton: {
        paddingVertical: 12,
        paddingHorizontal: 24,
        borderRadius: 20,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },

    sideUser: {
        alignItems: 'center',
        width: '33%',
    },
    crownContainer: {
        position: 'absolute',
        top: -12,
        zIndex: 1,
    },
    topName: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
        marginVertical: 8,
        maxWidth: 80,
    },
    coinsBadgeTop: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginBottom: 8,
        gap: 4,
    },
    topCoinsText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#FFFFFF',
    },
    rankBadge: {
        width: 28,
        height: 28,
        borderRadius: 14,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 4,
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginHorizontal: 16,
        padding: 16,
        borderRadius: 16,
        borderWidth: 1,
        marginBottom: 12,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    rankContainer: {
        width: 32,
        alignItems: 'center',
    },
    rankText: {
        fontWeight: '700',
        fontSize: 16,
    },
    nameContainer: {
        flex: 1,
        marginLeft: 4,
    },
    nameText: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    emailText: {
        fontSize: 12,
        fontWeight: '400',
        marginBottom: 4,
    },
    streakContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    streakText: {
        fontSize: 11,
        fontWeight: '500',
    },
    streakBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        borderWidth: 1,
        marginTop: 4,
        gap: 4,
    },
    streakBadgeText: {
        fontSize: 11,
        fontWeight: '600',
    },
    scoreContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        marginTop: 2,
    },
    scoreText: {
        fontSize: 11,
        fontWeight: '500',
    },
    rankBadgeSmall: {
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Dream11-style Winner Spotlight
    winnerSpotlight: {
        alignItems: 'center',
        paddingVertical: 32,
        paddingHorizontal: 24,
        marginHorizontal: 16,
        marginBottom: 24,
        borderRadius: 20,
        borderWidth: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    winnerCrownContainer: {
        marginBottom: 16,
    },
    winnerAvatarContainer: {
        position: 'relative',
        marginBottom: 16,
    },
    winnerRankBadge: {
        position: 'absolute',
        bottom: -8,
        right: -8,
        width: 32,
        height: 32,
        borderRadius: 16,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 3,
        borderColor: '#FFFFFF',
    },
    winnerRankText: {
        fontSize: 16,
        fontWeight: '800',
        color: '#FFFFFF',
    },
    winnerName: {
        fontSize: 22,
        fontWeight: '800',
        marginBottom: 4,
        textAlign: 'center',
    },
    winnerEmail: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 20,
        textAlign: 'center',
    },
    winnerStatsContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    winnerStatItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        gap: 6,
    },
    winnerStatText: {
        fontSize: 14,
        fontWeight: '700',
    },
    sectionTitleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        marginBottom: 16,
        gap: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '700',
    },
    sectionTitleLine: {
        flex: 1,
        height: 1,
    },
    // Improved List Item Styles
    listItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 16,
        marginHorizontal: 16,
        marginBottom: 12,
        borderRadius: 16,
        borderWidth: 1,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 4,
        elevation: 2,
    },
    leftSection: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    rankBadgeList: {
        width: 28,
        height: 28,
        borderRadius: 14,
        alignItems: 'center',
        justifyContent: 'center',
    },
    rankBadgeText: {
        fontSize: 14,
        fontWeight: '800',
        color: '#FFFFFF',
    },
    middleSection: {
        flex: 1,
        marginLeft: 12,
    },
    listUserName: {
        fontSize: 16,
        fontWeight: '700',
        marginBottom: 2,
    },
    listUserEmail: {
        fontSize: 13,
        fontWeight: '500',
        marginBottom: 8,
    },
    statsRow: {
        flexDirection: 'row',
        gap: 8,
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        gap: 4,
    },
    statText: {
        fontSize: 12,
        fontWeight: '600',
    },
    rightSection: {
        alignItems: 'flex-end',
    },
    coinsBadgeList: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
    },
    coinsTextList: {
        fontSize: 13,
        fontWeight: '700',
        color: '#FFFFFF',
    },
    // UEST Coin Icon Styles
    uestCoinIcon: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: '#FD904B',
        alignItems: 'center',
        justifyContent: 'center',
    },
    uestCoinText: {
        fontSize: 10,
        fontWeight: '800',
        color: '#FFFFFF',
    },
    uestCoinIconLarge: {
        width: 18,
        height: 18,
        borderRadius: 9,
        backgroundColor: '#FD904B',
        alignItems: 'center',
        justifyContent: 'center',
    },
    uestCoinTextLarge: {
        fontSize: 12,
        fontWeight: '800',
        color: '#FFFFFF',
    },
});