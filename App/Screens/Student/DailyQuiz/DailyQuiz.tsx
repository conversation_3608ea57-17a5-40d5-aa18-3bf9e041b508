import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Linking,
  Dimensions,
} from 'react-native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import {IMAGE_CONSTANT} from '../../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import { WebUrl } from '../../../config/apiUrl';
import AsyncStorage from '@react-native-async-storage/async-storage';

const {width} = Dimensions.get('window');

export default function DailyQuiz() {
  const {isDarkMode} = IndexStyle();
  const navigation = useNavigation<any>();

  const rewardTiers = [
    {score: '50%', coins: 0, color: '#666666', icon: 'trending-down'},
    {score: '60%', coins: 1, color: '#555555', icon: 'trending-up'},
    {score: '70%', coins: 2, color: '#444444', icon: 'trending-up'},
    {score: '80%', coins: 3, color: '#333333', icon: 'trending-up'},
    {score: '90%', coins: 4, color: '#222222', icon: 'star'},
    {score: '100%', coins: 5, color: '#FD904B', icon: 'star'}, // Only 100% gets the custom color
  ];

  const streakBonuses = [
    {title: 'Daily Streak', coins: '+1 coin/day', icon: 'local-fire-department'},
    {title: 'Weekly Challenge', coins: '+5 coins', icon: 'emoji-events'},
    {title: 'Perfect Score', coins: '+3 coins', icon: 'stars'},
  ];

  const quizStats = [
    {label: 'Questions', value: '10', icon: 'quiz', color: '#333333'},
    {label: 'Duration', value: '8 min', icon: 'timer', color: '#444444'},
    {label: 'Max Coins', value: '25', icon: 'monetization-on', color: '#FD904B'}, // Custom color for coins
    {label: 'Difficulty', value: 'Medium', icon: 'trending-up', color: '#555555'},
  ];

  const themeColors = {
    background: isDarkMode ? '#000000' : '#FFFFFF',
    card: isDarkMode ? '#1A1A1A' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    muted: isDarkMode ? '#CCCCCC' : '#666666',
    border: isDarkMode ? '#333333' : '#E0E0E0',
    iconBg: isDarkMode ? '#333333' : '#F0F0F0',
    accent: '#FD904B', // Custom orange color
    success: '#000000',
    warning: '#666666',
    error: '#333333',
  };

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Daily Quiz" onBackPress={() => {}} />
      <SafeAreaView
        style={[styles.container, {backgroundColor: themeColors.background}]}
        edges={['left', 'right']}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>

          {/* Hero Section */}
          <View style={[styles.heroSection, {backgroundColor: themeColors.card}]}>
            <View style={styles.heroContent}>
              <View style={styles.heroIcon}>
                <View style={[styles.iconGradient, {backgroundColor: themeColors.accent}]}>
                  <MaterialIcons name="quiz" size={32} color="#FFFFFF" />
                </View>
              </View>
              <Text style={[styles.heroTitle, {color: themeColors.text}]}>
                Daily Current Affairs Quiz
              </Text>
              <Text style={[styles.heroSubtitle, {color: themeColors.muted}]}>
                Stay informed, test your knowledge, and earn exclusive rewards!
              </Text>
            </View>
          </View>

          {/* Quick Stats Grid */}
          <View style={styles.statsGrid}>
            {quizStats.map((stat, index) => (
              <View key={index} style={[styles.statCard, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}>
                <View style={[styles.statIcon, {backgroundColor: stat.color + '20'}]}>
                  <MaterialIcons name={stat.icon} size={20} color={stat.color} />
                </View>
                <Text style={[styles.statValue, {color: themeColors.text}]}>{stat.value}</Text>
                <Text style={[styles.statLabel, {color: themeColors.muted}]}>{stat.label}</Text>
              </View>
            ))}
          </View>
          {/* Main CTA Button */}
          <TouchableOpacity
            style={[styles.primaryBtn, {backgroundColor: themeColors.accent}]}
            onPress={async () => {
              try {
                const token = await AsyncStorage.getItem('token');
                if (token) {
                  Linking.openURL(`${WebUrl}/mock-test/?token=${token}`);
                }
              } catch (error) {
                // Handle error silently
              }
            }}>
            <MaterialIcons name="play-arrow" size={24} color="#FFFFFF" />
            <Text style={styles.primaryBtnText}>Start Daily Quiz</Text>
            <MaterialIcons name="arrow-forward" size={20} color="#FFFFFF" />
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionBtn, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}
              onPress={() => navigation.navigate('DailyQuizLeaderboard')}>
              <View style={[styles.actionBtnIcon, {backgroundColor: themeColors.iconBg}]}>
                <MaterialIcons name="leaderboard" size={20} color={themeColors.text} />
              </View>
              <Text style={[styles.actionBtnText, {color: themeColors.text}]}>Leaderboard</Text>
              <MaterialIcons name="chevron-right" size={20} color={themeColors.muted} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionBtn, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}
              onPress={() => navigation.navigate('DailyResults')}>
              <View style={[styles.actionBtnIcon, {backgroundColor: themeColors.iconBg}]}>
                <MaterialIcons name="analytics" size={20} color={themeColors.text} />
              </View>
              <Text style={[styles.actionBtnText, {color: themeColors.text}]}>My Results</Text>
              <MaterialIcons name="chevron-right" size={20} color={themeColors.muted} />
            </TouchableOpacity>
          </View>

          {/* Rewards Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="stars" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, {color: themeColors.text}]}>
                Coin Rewards
              </Text>
            </View>

            <View style={styles.rewardsGrid}>
              {rewardTiers.map((item, index) => (
                <View
                  key={index}
                  style={[styles.rewardCard, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}>
                  <View style={[styles.rewardIcon, {backgroundColor: themeColors.iconBg}]}>
                    <MaterialIcons name={item.icon} size={18} color={item.color} />
                  </View>
                  <Text style={[styles.rewardScore, {color: themeColors.text}]}>
                    {item.score}
                  </Text>
                  <View style={[styles.rewardPill, {backgroundColor: item.color}]}>
                    <MaterialIcons name="monetization-on" size={12} color="#FFFFFF" />
                    <Text style={styles.pillText}>{item.coins}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Streak Bonuses Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="local-fire-department" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, {color: themeColors.text}]}>
                Streak Bonuses
              </Text>
            </View>

            <View style={styles.streakContainer}>
              {streakBonuses.map((item, index) => (
                <View
                  key={index}
                  style={[styles.streakCard, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}>
                  <View style={[styles.streakIcon, {backgroundColor: themeColors.iconBg}]}>
                    <MaterialIcons name={item.icon} size={20} color={themeColors.text} />
                  </View>
                  <View style={styles.streakContent}>
                    <Text style={[styles.streakTitle, {color: themeColors.text}]}>
                      {item.title}
                    </Text>
                    <Text style={[styles.streakReward, {color: themeColors.muted}]}>
                      {item.coins}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Badges Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="emoji-events" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, {color: themeColors.text}]}>
                Achievement Badges
              </Text>
            </View>

            <View style={styles.badgeContainer}>
              {[
                {image: IMAGE_CONSTANT.BADGE100, title: '100 Coins', bgColor: '#333333'},
                {image: IMAGE_CONSTANT.BADGE1000, title: '500 Coins', bgColor: '#444444'},
                {image: IMAGE_CONSTANT.BADGE10000, title: '1000 Coins', bgColor: '#FD904B'}, // Custom color for highest badge
                {image: IMAGE_CONSTANT.BADGEMONTH, title: '30 Days', bgColor: '#555555'},
                {image: IMAGE_CONSTANT.BADGEYEAR, title: '365 Days', bgColor: '#666666'},
                {image: IMAGE_CONSTANT.BADGESTREAK, title: 'Daily Streak', bgColor: '#777777'},
              ].map((badge, index) => (
                <View key={index} style={[styles.badgeItem, {backgroundColor: themeColors.card, borderColor: themeColors.border}]}>
                  <View style={[styles.badgeImageContainer, {backgroundColor: badge.bgColor === '#FD904B' ? badge.bgColor : themeColors.iconBg}]}>
                    <Image source={badge.image} style={styles.badgeImage} />
                  </View>
                  <Text style={[styles.badgeLabel, {color: themeColors.text}]}>
                    {badge.title}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },

  // Hero Section
  heroSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    marginBottom: 20,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroIcon: {
    marginBottom: 16,
  },
  iconGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  heroSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },

  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 56) / 2,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },

  // Primary Button
  primaryBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 24,
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    gap: 12,
  },
  primaryBtnText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  // Action Buttons
  actionButtons: {
    paddingHorizontal: 20,
    marginBottom: 32,
    gap: 12,
  },
  actionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  actionBtnIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionBtnText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },

  // Section Containers
  sectionContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
  },

  // Rewards Grid
  rewardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  rewardCard: {
    flex: 1,
    minWidth: (width - 64) / 3,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  rewardIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  rewardScore: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
  },
  rewardPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  pillText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  // Streak Container
  streakContainer: {
    gap: 12,
  },
  streakCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  streakIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  streakContent: {
    flex: 1,
  },
  streakTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  streakReward: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Badge Container
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  badgeItem: {
    flex: 1,
    minWidth: (width - 64) / 3,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  badgeImageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  badgeImage: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },
  badgeLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
