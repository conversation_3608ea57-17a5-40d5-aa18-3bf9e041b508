import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  TextInput,
  Modal,
  Animated,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import {getThemeColors, IMAGE_CONSTANT} from '../../../Utils/Constants';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { getAllStoreItems, getUserCoins, StoreItem } from '../../../services/storeService';
import { processPurchase } from '../../../services/storePurchaseService';


interface Product extends StoreItem {
  // Additional UI properties for mobile app
  isComingSoon?: boolean;
}

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  count?: number;
}

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
  image: string | null;
}

// Category configuration constants
const CATEGORY_CONFIG = {
  icons: {
    'toys': 'game-controller',
    'stationery': 'pencil',
    'sports': 'football',
    'electronics': 'phone-portrait',
    'books': 'book',
    'others': 'ellipsis-horizontal',
  } as { [key: string]: string },
  colors: {
    'toys': '#E91E63',
    'stationery': '#4CAF50',
    'sports': '#FF9800',
    'electronics': '#2196F3',
    'books': '#9C27B0',
    'others': '#607D8B',
  } as { [key: string]: string },
};

const Store: React.FC = () => {
  const navigation = useNavigation<any>();
  const {isDarkMode} = IndexStyle();
  const themeColors = getThemeColors(isDarkMode);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(500));
  const [userCoins, setUserCoins] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [isLoadingCoins, setIsLoadingCoins] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' ||
      product.category.toLowerCase() === selectedCategory.toLowerCase();
    const matchesSearch = product.name.toLowerCase().includes(searchText.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  useEffect(() => {
    fetchStoreData();
    fetchUserCoins();
  }, []);

  const fetchStoreData = async () => {
    setIsLoadingProducts(true);

    try {
      const result = await getAllStoreItems();
      if (result.success && result.data) {
        const products = result.data.map(item => ({ ...item, isComingSoon: false }));
        setProducts(products);
        updateCategoriesFromProducts(products);
        setError(null);
      } else {
        setError('Failed to load products');
      }
    } catch (error) {
      setError('Failed to load products');
    }

    setIsLoadingProducts(false);
  };

  const updateCategoriesFromProducts = (products: Product[]) => {
    const uniqueCategories = [...new Set(products.map(p => p.category))];

    const productCategories: Category[] = uniqueCategories.map(categoryId => ({
      id: categoryId,
      name: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
      icon: CATEGORY_CONFIG.icons[categoryId.toLowerCase()] || 'ellipsis-horizontal',
      color: CATEGORY_CONFIG.colors[categoryId.toLowerCase()] || '#607D8B',
      count: products.filter(p => p.category === categoryId).length,
    }));

    setCategories([
      {id: 'all', name: 'All', icon: 'apps', color: themeColors.accent},
      ...productCategories
    ]);
  };

  const fetchUserCoins = async () => {
    setIsLoadingCoins(true);
    try {
      const result = await getUserCoins();
      setUserCoins(result.success && result.data ? result.data.coins : 0);
    } catch (error) {
      setUserCoins(0);
    }
    setIsLoadingCoins(false);
  };

  const addToCart = (product: Product) => {
    if (product.quantity <= 0) {
      Alert.alert('Out of Stock', 'This item is currently out of stock.');
      return;
    }

    const existingItem = cartItems.find(item => item.id === product.id);
    if (existingItem && existingItem.quantity >= product.quantity) {
      Alert.alert('Quantity Limit', `Only ${product.quantity} items available in stock.`);
      return;
    }

    if (existingItem) {
      setCartItems(prev => prev.map(item =>
        item.id === product.id ? { ...item, quantity: item.quantity + 1 } : item
      ));
    } else {
      setCartItems(prev => [...prev, {
        id: product.id,
        name: product.name,
        coinPrice: product.coinPrice,
        quantity: 1,
        image: product.image,
      }]);
    }
  };

  const removeFromCart = (productId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== productId));
  };

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      setCartItems(prev => prev.map(item =>
        item.id === productId ? { ...item, quantity } : item
      ));
    }
  };

  const getTotalCoins = () => cartItems.reduce((total, item) => total + (item.coinPrice * item.quantity), 0);
  const getCartItemsCount = () => cartItems.reduce((total, item) => total + item.quantity, 0);

  const showCart = () => {
    setIsCartVisible(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const hideCart = () => {
    Animated.timing(slideAnim, {
      toValue: 500,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsCartVisible(false);
    });
  };

  // Checkout functionality
  const handleCheckout = async () => {
    const totalCoins = getTotalCoins();

    if (totalCoins === 0) {
      Alert.alert('Empty Cart', 'Please add items to your cart before checkout.');
      return;
    }

    if (userCoins < totalCoins) {
      Alert.alert(
        'Insufficient UEST Coins',
        `You need ${totalCoins} UEST Coins but only have ${userCoins} coins. Would you like to purchase more coins?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Buy Coins',
            onPress: () => {
              hideCart();
              navigation.navigate('AddCoins');
            },
          },
        ]
      );
      return;
    }

    setIsLoading(true);
    try {
      const purchaseCartItems = cartItems.map(item => ({
        id: item.id,
        name: item.name,
        coinPrice: item.coinPrice,
        quantity: item.quantity,
      }));

      const result = await processPurchase({
        cartItems: purchaseCartItems,
        totalCoins: totalCoins,
      });

      if (result.success) {
        setUserCoins(prev => prev - totalCoins);
        setCartItems([]);
        hideCart();

        Alert.alert(
          'Order Successful!',
          `Your order has been placed successfully. ${totalCoins} UEST Coins have been deducted from your account.\n\nOrder ID: ${result.data?.orderId || 'N/A'}`,
          [{ text: 'OK', onPress: fetchUserCoins }]
        );
      } else {
        Alert.alert('Purchase Failed', result.error || 'Something went wrong during checkout.');
      }
    } catch (error) {
      Alert.alert('Checkout Failed', 'Network error. Please check your connection and try again.');
    }

    setIsLoading(false);
  };

  const renderCategoryItem = ({item}: {item: Category}) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        {
          backgroundColor: selectedCategory === item.id ? item.color : themeColors.surface,
          borderColor: selectedCategory === item.id ? item.color : themeColors.border,
        },
      ]}
      onPress={() => setSelectedCategory(item.id)}>
      <MaterialIcons
        name={item.icon}
        size={24}
        color={selectedCategory === item.id ? themeColors.background : item.color}
      />
      <Text
        style={[
          styles.categoryText,
          {
            color: selectedCategory === item.id ? themeColors.background : themeColors.text,
            fontWeight: selectedCategory === item.id ? '600' : '500',
          },
        ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderProductItem = ({item}: {item: Product}) => (
    <TouchableOpacity style={[styles.productCard, {backgroundColor: themeColors.card}]}>
      {/* Product Image Container */}
      <View style={styles.imageSection}>
        {/* Wishlist Icon */}
        <TouchableOpacity style={styles.wishlistIcon}>
          <Ionicons name="heart-outline" size={24} color={themeColors.muted} />
        </TouchableOpacity>

        {/* Product Image */}
        <View style={styles.productImageContainer}>
          {item.image ? (
            <Image
              source={{ uri: item.image }}
              style={styles.productImage}
              resizeMode="cover"
              defaultSource={IMAGE_CONSTANT.CLASSMATE} // Fallback image
            />
          ) : (
            <Image
              source={IMAGE_CONSTANT.CLASSMATE}
              style={styles.productImage}
              resizeMode="cover"
            />
          )}
        </View>

        {/* Coming Soon Badge */}
        {item.isComingSoon && (
          <View style={styles.comingSoonImageBadge}>
            <Text style={styles.comingSoonImageText}>Coming Soon</Text>
          </View>
        )}
      </View>

      {/* Product Content Below Image */}
      <View style={styles.contentSection}>
        {/* Product Name and Rating */}
        <View style={styles.titleRow}>
          <Text style={[styles.productName, {color: themeColors.text}]} numberOfLines={1}>
            {item.name}
          </Text>
        </View>

        {/* Category and Price */}
        <View style={styles.categoryPriceRow}>
          <Text style={[styles.productCategory, {color: themeColors.muted}]}>
            {item.category.charAt(0).toUpperCase() + item.category.slice(1).replace('-', ' ')}
          </Text>
          <Text style={[styles.priceText, {color: themeColors.text}]}>
            {item.coinPrice} UEST Coins
          </Text>
        </View>

        {/* Additional Info */}
        <View style={styles.additionalInfo}>
          <View style={styles.infoRow}>
            <View style={styles.infoIcon}>
              <Image
                source={IMAGE_CONSTANT.NEWUESTCOIN}
                style={styles.coinIcon}
                resizeMode="contain"
              />
            </View>
            <Text style={[styles.infoText, {color: themeColors.muted}]} numberOfLines={2}>
              {item.description || 'No description available'}
            </Text>

            {/* Stock Status */}
            <View style={styles.stockInfo}>
              <Text style={[
                styles.stockText,
                {
                  color: item.quantity > 0 ? themeColors.success || '#4CAF50' : themeColors.error || '#F44336'
                }
              ]}>
                {item.quantity > 0 ? `${item.quantity} in stock` : 'Out of stock'}
              </Text>
            </View>
          </View>
        </View>

        {/* Add to Cart Button */}
        <TouchableOpacity
          style={[
            styles.addToCartButton,
            {
              backgroundColor: item.quantity > 0 ? themeColors.accent : themeColors.muted,
              opacity: item.quantity > 0 ? 1 : 0.6
            }
          ]}
          onPress={() => addToCart(item)}
          disabled={item.quantity <= 0 || item.isComingSoon}
        >
          <Ionicons name="bag-add" size={16} color="#FFFFFF" />
          <Text style={styles.addToCartText}>
            {item.quantity <= 0 ? 'Out of Stock' : item.isComingSoon ? 'Coming Soon' : 'Add to Cart'}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[styles.container, {backgroundColor: themeColors.background}]}
        edges={['left', 'right']}>
        <View style={styles.headerContainer}>
          <NavigationHeader
            title="UEST Store"
            isBack={true}
            onBackPress={() => navigation.goBack()}
          />

          {/* Order History Icon */}
          <TouchableOpacity
            onPress={() => navigation.navigate('OrderHistory')}
            style={styles.orderHistoryIcon}
          >
            <Ionicons name="receipt-outline" size={24} color="#FFFFFF" />
          </TouchableOpacity>

          {/* Cart Icon */}
          <TouchableOpacity onPress={showCart} style={styles.cartIconOverlay}>
            <Ionicons name="bag" size={24} color="#FFFFFF" />
            {getCartItemsCount() > 0 && (
              <View style={[styles.cartBadge, {backgroundColor: themeColors.accent}]}>
                <Text style={styles.cartBadgeText}>{getCartItemsCount()}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
        
        <ScrollView
          style={[styles.content, {backgroundColor: themeColors.background}]}
          showsVerticalScrollIndicator={false}>

          {/* Search Bar */}
          <View style={[styles.searchContainer, {backgroundColor: themeColors.surface}]}>
            <Ionicons name="search" size={20} color={themeColors.muted} />
            <TextInput
              style={[styles.searchInput, {color: themeColors.text}]}
              placeholder="Search products..."
              placeholderTextColor={themeColors.muted}
              value={searchText}
              onChangeText={setSearchText}
            />
            <TouchableOpacity style={styles.filterButton}>
              <Ionicons name="filter" size={20} color={themeColors.accent} />
            </TouchableOpacity>
          </View>

          {/* Categories */}
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, {color: themeColors.text}]}>
              Categories
            </Text>
            <FlatList
              data={categories}
              renderItem={renderCategoryItem}
              keyExtractor={item => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContainer}
            />
          </View>

          {/* Coin Balance Banner */}
          <View style={[styles.bannerContainer, {backgroundColor: themeColors.accentLight}]}>
            <View style={styles.bannerContent}>
              <Text style={[styles.bannerTitle, {color: themeColors.accentDark}]}>
                Your UEST Coins
              </Text>
              {isLoadingCoins ? (
                <View style={styles.coinLoadingContainer}>
                  <ActivityIndicator size="small" color={themeColors.accent} />
                  <Text style={[styles.bannerSubtitle, {color: themeColors.text, marginLeft: 8}]}>
                    Loading balance...
                  </Text>
                </View>
              ) : (
                <Text style={[styles.bannerSubtitle, {color: themeColors.text}]}>
                  Balance: {userCoins} coins
                </Text>
              )}
              <TouchableOpacity
                style={[styles.bannerButton, {backgroundColor: themeColors.accent}]}
                onPress={() => navigation.navigate('AddCoins')}>
                <Text style={[styles.bannerButtonText, {color: themeColors.background}]}>
                  Add Coins
                </Text>
              </TouchableOpacity>
            </View>
            <Image
              source={IMAGE_CONSTANT.NEWUESTCOIN}
              style={styles.bannerImage}
              resizeMode="contain"
            />
          </View>

          {/* Products Grid */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, {color: themeColors.text}]}>
                {selectedCategory === 'all' ? 'All Products' :
                 categories.find(c => c.id === selectedCategory)?.name || 'Products'}
              </Text>
              {!isLoadingProducts && (
                <Text style={[styles.productCount, {color: themeColors.muted}]}>
                  {filteredProducts.length} items
                </Text>
              )}

            </View>

            {/* Loading State */}
            {isLoadingProducts ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={themeColors.accent} />
                <Text style={[styles.loadingText, {color: themeColors.muted}]}>
                  Loading products...
                </Text>
              </View>
            ) : error ? (
              /* Error State */
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle-outline" size={48} color={themeColors.error || '#F44336'} />
                <Text style={[styles.errorText, {color: themeColors.error || '#F44336'}]}>
                  {error}
                </Text>
                <TouchableOpacity
                  style={[styles.retryButton, {backgroundColor: themeColors.accent}]}
                  onPress={fetchStoreData}
                >
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : filteredProducts.length === 0 ? (
              /* Empty State */
              <View style={styles.emptyContainer}>
                <Ionicons name="bag-outline" size={48} color={themeColors.muted} />
                <Text style={[styles.emptyText, {color: themeColors.muted}]}>
                  {searchText ? 'No products found matching your search' : 'No products available'}
                </Text>
              </View>
            ) : (
              /* Products List */
              <View style={styles.productsList}>
                {filteredProducts.map((item, index) => (
                  <View key={item.id}>
                    {renderProductItem({item})}
                    {index < filteredProducts.length - 1 && (
                      <View style={styles.productSeparator} />
                    )}
                  </View>
                ))}
              </View>
            )}
          </View>
        </ScrollView>

        {/* Cart Modal */}
        <Modal
          visible={isCartVisible}
          transparent={true}
          animationType="none"
          onRequestClose={hideCart}
        >
          <View style={styles.modalOverlay}>
            <TouchableOpacity
              style={styles.modalBackground}
              activeOpacity={1}
              onPress={hideCart}
            />
            <Animated.View
              style={[
                styles.cartModal,
                {
                  backgroundColor: themeColors.background,
                  transform: [{translateY: slideAnim}]
                }
              ]}
            >
              {/* Cart Header */}
              <View style={[styles.cartHeader, {borderBottomColor: themeColors.border}]}>
                <Text style={[styles.cartTitle, {color: themeColors.text}]}>
                  My Cart ({getCartItemsCount()} items)
                </Text>
                <TouchableOpacity onPress={hideCart}>
                  <Ionicons name="close" size={24} color={themeColors.text} />
                </TouchableOpacity>
              </View>

              {/* Cart Items */}
              <ScrollView style={styles.cartContent} showsVerticalScrollIndicator={false}>
                {cartItems.length === 0 ? (
                  <View style={styles.emptyCart}>
                    <Ionicons name="bag-outline" size={64} color={themeColors.muted} />
                    <Text style={[styles.emptyCartText, {color: themeColors.muted}]}>
                      Your cart is empty
                    </Text>
                  </View>
                ) : (
                  cartItems.map((item) => {
                    // Find product details for category info
                    const productDetails = products.find(p => p.id === item.id);
                    const categoryName = categories.find(c => c.id === productDetails?.category)?.name || 'Product';

                    return (
                      <View key={item.id} style={[styles.cartItem, {borderBottomColor: themeColors.border}]}>
                        {item.image ? (
                          <Image
                            source={{ uri: item.image }}
                            style={styles.cartItemImage}
                            resizeMode="cover"
                            defaultSource={IMAGE_CONSTANT.CLASSMATE}
                          />
                        ) : (
                          <Image
                            source={IMAGE_CONSTANT.CLASSMATE}
                            style={styles.cartItemImage}
                            resizeMode="cover"
                          />
                        )}
                        <View style={styles.cartItemDetails}>
                          <Text style={[styles.cartItemName, {color: themeColors.text}]} numberOfLines={2}>
                            {item.name}
                          </Text>
                          <Text style={[styles.cartItemCategory, {color: themeColors.muted}]}>
                            {categoryName}
                          </Text>
                          <View style={styles.priceRow}>
                            <Image
                              source={IMAGE_CONSTANT.NEWUESTCOIN}
                              style={styles.cartCoinIcon}
                              resizeMode="contain"
                            />
                            <Text style={[styles.cartItemPrice, {color: themeColors.accent}]}>
                              {item.coinPrice} UEST Coins
                            </Text>
                          </View>
                        </View>
                        <View style={styles.quantityControls}>
                          <TouchableOpacity
                            onPress={() => updateQuantity(item.id, item.quantity - 1)}
                            style={[styles.quantityButton, {borderColor: themeColors.border, backgroundColor: themeColors.surface}]}
                          >
                            <Ionicons name="remove" size={16} color={themeColors.text} />
                          </TouchableOpacity>
                          <Text style={[styles.quantityText, {color: themeColors.text}]}>
                            {item.quantity}
                          </Text>
                          <TouchableOpacity
                            onPress={() => updateQuantity(item.id, item.quantity + 1)}
                            style={[styles.quantityButton, {borderColor: themeColors.border, backgroundColor: themeColors.surface}]}
                          >
                            <Ionicons name="add" size={16} color={themeColors.text} />
                          </TouchableOpacity>
                        </View>
                      </View>
                    );
                  })
                )}
              </ScrollView>

              {/* Cart Footer */}
              {cartItems.length > 0 && (
                <View style={[styles.cartFooter, {borderTopColor: themeColors.border}]}>
                  <View style={styles.totalSection}>
                    <View style={styles.coinsInfo}>
                      <Text style={[styles.userCoinsText, {color: themeColors.muted}]}>
                        Your Balance: {userCoins} UEST Coins
                      </Text>
                      <Text style={[styles.totalLabel, {color: themeColors.text}]}>
                        Total: {getTotalCoins()} UEST Coins
                      </Text>
                      {userCoins < getTotalCoins() && (
                        <Text style={[styles.insufficientText, {color: '#FF5722'}]}>
                          Insufficient coins! Need {getTotalCoins() - userCoins} more coins.
                        </Text>
                      )}
                    </View>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.checkoutButton,
                      {
                        backgroundColor: isLoading ? themeColors.muted : themeColors.accent,
                        opacity: isLoading ? 0.7 : 1,
                      }
                    ]}
                    onPress={handleCheckout}
                    disabled={isLoading}
                  >
                    <Text style={styles.checkoutButtonText}>
                      {isLoading ? 'Processing...' : 'Proceed to Checkout'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </Animated.View>
          </View>
        </Modal>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    position: 'relative',
  },
  cartIconOverlay: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    padding: 8,
  },
  orderHistoryIcon: {
    position: 'absolute',
    top: 40,
    right: 70,
    zIndex: 10,
    padding: 8,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontWeight: '500',
  },
  filterButton: {
    padding: 4,
  },
  sectionContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  productCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesContainer: {
    paddingRight: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  bannerContainer: {
    marginHorizontal: 16,
    marginTop: 24,
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bannerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  bannerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
    opacity: 0.8,
  },
  bannerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  bannerButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  bannerImage: {
    width: 60,
    height: 60,
    marginLeft: 16,
  },
  productsList: {
    paddingBottom: 20,
  },
  productSeparator: {
    height: 16,
  },
  productCard: {
    borderRadius: 16,
    marginHorizontal: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.12,
    shadowRadius: 6,
    overflow: 'hidden',
  },
  imageSection: {
    position: 'relative',
    height: 250,
    backgroundColor: '#F8F8F8',
  },

  wishlistIcon: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 8,
    zIndex: 3,
  },

  comingSoonImageBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  comingSoonImageText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  contentSection: {
    padding: 16,
  },

  productImageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryPriceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  productCategory: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  priceText: {
    fontSize: 16,
    fontWeight: '700',
  },
  comingSoonBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  comingSoonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  additionalInfo: {
    marginTop: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  infoText: {
    fontSize: 12,
    lineHeight: 16,
    flex: 1,
  },
  coinIcon: {
    width: 16,
    height: 16,
  },
  productName: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 22,
    flex: 1,
    marginRight: 8,
  },
  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
  },
  ratingValue: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },

  // Cart Styles
  cartButton: {
    position: 'relative',
    padding: 8,
  },
  cartBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 12,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalBackground: {
    flex: 1,
  },
  cartModal: {
    height: '80%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  cartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  cartTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cartContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyCart: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyCartText: {
    fontSize: 16,
    marginTop: 16,
  },
  cartItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  cartItemImage: {
    width: 70,
    height: 70,
    borderRadius: 12,
  },
  cartItemDetails: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    lineHeight: 20,
  },
  cartItemCategory: {
    fontSize: 12,
    fontWeight: '400',
    marginBottom: 6,
    textTransform: 'capitalize',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cartCoinIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 12,
    minWidth: 24,
    textAlign: 'center',
  },
  cartFooter: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 20,
    borderTopWidth: 1,
  },
  totalSection: {
    marginBottom: 16,
  },
  coinsInfo: {
    alignItems: 'center',
  },
  userCoinsText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  insufficientText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 4,
  },
  checkoutButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Loading, Error, and Empty States
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 12,
  },
  // Stock Info
  stockInfo: {
    marginTop: 4,
  },
  stockText: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Coin Loading
  coinLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },


});

export default Store;
