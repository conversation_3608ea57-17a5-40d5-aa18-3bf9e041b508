import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import moment from 'moment';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { getClasswork } from '../../services/studentClassesService';
import CurvHeader from '../../CommonComponents/CurvHeader';
import Badge from '../../CommonComponents/Badge';
import RenderHTML from 'react-native-render-html';
import { Classwork } from '../../Utils/Interfaces';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import CommonCalendarStrip from '../../CommonComponents/CommonCalendarStrip';

const { width } = Dimensions.get('window');

const ClassWorkScreen = () => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format('YYYY-MM-DD'),
  );
  const [works, setWorks] = useState<Classwork[]>([]);
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();

  const theme = {
    background: isDarkMode ? '#000000' : '#FAFAFA',
    card: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#1A1A1A',
    subText: isDarkMode ? '#AAAAAA' : '#555555',
    accent: '#FD904B',
    border: isDarkMode ? '#333333' : '#E0E0E0',
  };

  useEffect(() => {
    loadClasswork(selectedDate);
  }, [selectedDate]);

  const loadClasswork = async (date: string) => {
    const { data } = await getClasswork(date);
    setWorks(data);
  };

  const renderWorkItem = (item: Classwork) => (
    <View style={[styles.card, { backgroundColor: theme.card }]} key={item.id}>
      <View style={styles.headerRow}>
        <Badge
          text={item.subject_name}
          backgroundColor={theme.accent}
          textColor="#fff"
        />
        <Text style={[styles.teacherText, { color: theme.subText }]}>
          {item.faculty_name}
        </Text>
      </View>
      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.text }]}>
          Classwork: {item.title}
        </Text>
        <RenderHTML
          contentWidth={width - 40}
          source={{ html: item.description || '<p>No Content</p>' }}
        />
      </View>
    </View>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[styles.safeArea, { backgroundColor: theme.background }]}>
        <CurvHeader
          title="Classwork"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <CommonCalendarStrip setSelectedDate={setSelectedDate} selectedDate={selectedDate} />

        <ScrollView contentContainerStyle={styles.content}>
          {works.length === 0 ? (
            <Text style={[styles.noData, { color: theme.subText }]}>
              No data found
            </Text>
          ) : (
            works.map(renderWorkItem)
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export { ClassWorkScreen };

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    padding: 12,
    gap: 12,
  },
  card: {
    borderRadius: 12,
    padding: 14,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  section: {
    marginTop: 6,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  teacherText: {
    fontSize: 14,
    fontWeight: '500',
  },
  meta: {
    fontSize: 14,
    marginTop: 2,
  },
  noData: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 40,
  },
});
