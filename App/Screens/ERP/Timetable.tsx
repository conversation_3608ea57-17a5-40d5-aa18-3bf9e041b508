import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, SafeAreaView, StyleSheet } from 'react-native';
import moment from 'moment';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import CurvHeader from '../../CommonComponents/CurvHeader';
import { TimeTable } from '../../Utils/Interfaces';
import IndexStyle from '../../Theme/IndexStyle';
import Badge from '../../CommonComponents/Badge';
import { getTimetable } from '../../services/studentClassesService';
import CommonCalendarStrip from '../../CommonComponents/CommonCalendarStrip';

const TimetableScreen = () => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format('YYYY-MM-DD'),
  );
  const [timetableList, setTimetableList] = useState<TimeTable[]>([]);
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();

  const theme = {
    background: isDarkMode ? '#000000' : '#FAFAFA',
    card: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#1A1A1A',
    subText: isDarkMode ? '#AAAAAA' : '#555555',
    accent: '#FD904B',
    border: isDarkMode ? '#333333' : '#E0E0E0',
  };

  useEffect(() => {
    fetchTimetable(selectedDate);
  }, [selectedDate]);

  const fetchTimetable = async (date: string) => {
    try {
      const { data } = await getTimetable(date);
      setTimetableList(data || []);
    } catch (error) {
      console.error('Failed to load timetable:', error);
      setTimetableList([]);
    }
  };

  const renderTimetableItem = (item: TimeTable, index: number) => (
    <View style={[styles.card, { backgroundColor: theme.card }]} key={index}>
      <View style={styles.headerRow}>
        <Badge
          text={item.subject_name}
          backgroundColor="#FD904B"
          textColor="#fff"
        />
      </View>

      <View style={styles.section}>
        <Text style={[styles.label, { color: theme.text }]}>
          Time: {item.timeslot_start_time} - {item.timeslot_end_time}
        </Text>
        {item.timeslot_is_break === 'yes' && (
          <Text style={[styles.meta, { color: 'red' }]}>
            Break: {item.timeslot_break_name}
          </Text>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.background }]}>
        <CurvHeader
          title="Timetable"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <CommonCalendarStrip setSelectedDate={setSelectedDate} selectedDate={selectedDate} />


        <ScrollView contentContainerStyle={styles.content}>
          {timetableList.length === 0 ? (
            <Text style={[styles.noData, { color: theme.subText }]}>
              No timetable available
            </Text>
          ) : (
            timetableList.map(renderTimetableItem)
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export { TimetableScreen };

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    padding: 12,
    gap: 12,
  },
  card: {
    borderRadius: 12,
    padding: 14,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  section: {
    marginTop: 6,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  teacherText: {
    fontSize: 14,
    fontWeight: '500',
  },
  meta: {
    fontSize: 14,
    marginTop: 2,
  },
  noData: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 40,
  },
});
