import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { IMAGE_CONSTANT } from '../../Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import { getProfile } from '../../services/studentClassesService';
import { imgBaseUrl } from '../../config/apiUrl';
const { width } = Dimensions.get('window');

const categories = [
  { title: 'Annoucements', image: IMAGE_CONSTANT.ANNOUCEMENTS, route: 'Annoucements' },
  { title: 'Annual Calendar', image: IMAGE_CONSTANT.CALENDAR, route: 'AnnualCalender' },
  { title: 'Attendance', image: IMAGE_CONSTANT.ATTENDENCE, route: 'Attendance' },
  { title: 'Classwork', image: IMAGE_CONSTANT.CLASSWORK, route: 'Classwork' },
  { title: 'Homework', image: IMAGE_CONSTANT.HOMEWORK, route: 'Homework' },
  { title: 'Leaves', image: IMAGE_CONSTANT.LEAVES, route: 'Leave' },
  { title: 'Timetable', image: IMAGE_CONSTANT.TIMETABLE, route: 'Timetable' },
  { title: 'Documents', image: IMAGE_CONSTANT.DOCUMENTS, route: 'Documents' },

];

const Dashboard = ({ navigation }) => {
  const { isDarkMode } = IndexStyle();
  const [profileDetails, setProfileDetails] = useState({});

  const themeColors = {
    background: isDarkMode ? '#000000' : '#FFFFFF',
    card: isDarkMode ? '#1A1A1A' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    muted: isDarkMode ? '#CCCCCC' : '#666666',
    border: isDarkMode ? '#333333' : '#E0E0E0',
    iconBg: isDarkMode ? '#333333' : '#F0F0F0',
    accent: '#FD904B',
    success: '#000000',
    warning: '#666666',
    error: '#333333',
  };

  const getStudentDetails = async () => {
    try {
      const response = await getProfile();
      setProfileDetails(response.data);
    } catch (error) {
      console.error('Error fetching student details:', error);
    }
  };

  useEffect(() => {
    getStudentDetails();
  }, []);

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.card, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}
      onPress={() => navigation.navigate(item.route)}
    >
      <View style={[styles.cardIconContainer, { backgroundColor: themeColors.iconBg }]}>
        <Image source={item.image} style={styles.cardImage} />
      </View>
      <Text style={[styles.cardTitle, { color: themeColors.text }]}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.header, { backgroundColor: '#000000' }]}>
          <Text style={[styles.dashboardTitle, { color: '#FFFFFF' }]}>My Class</Text>
          <View style={styles.userContainer}>
            <View style={styles.userInfo}>
              <Text style={[styles.welcomeText, { color: themeColors.accent }]}>Welcome,</Text>
              <Text style={[styles.nameText, { color: '#FFFFFF' }]}>
                {profileDetails?.student?.firstName} {profileDetails?.student?.lastName}
              </Text>
              <Text style={[styles.detailText, { color: '#CCCCCC' }]}>
                Class: {profileDetails?.academicInfo?.get_class.className}
              </Text>
              <Text style={[styles.detailText, { color: '#CCCCCC' }]}>
                Classroom: {profileDetails?.academicInfo?.get_classroom.class_name}
              </Text>
              <Text style={[styles.detailText, { color: '#CCCCCC' }]}>
                Contact: {profileDetails?.student?.contact}
              </Text>
            </View>
            <Image
              source={{
                uri: `${imgBaseUrl}/${profileDetails?.student?.get_student_details?.photo}`,
              }}
              style={[styles.avatar, { borderColor: themeColors.border }]}
            />
          </View>
        </View>
        <View style={[styles.gridContainer, { backgroundColor: themeColors.background }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Quick Links</Text>
          <FlatList
            data={categories}
            renderItem={renderItem}
            keyExtractor={(item, index) => index.toString()}
            numColumns={2}
            columnWrapperStyle={styles.columnWrapper}
            contentContainerStyle={styles.flatListContent}
          />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 15,
  },
  dashboardTitle: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 16,
  },
  userContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    marginBottom: 25,
  },
  userInfo: {
    flex: 1,
    paddingRight: 16,
  },
  welcomeText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 4,
  },
  nameText: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 4,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
  },
  gridContainer: {
    flex: 1,
    padding: 16,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  flatListContent: {
    paddingBottom: 32,
  },
  card: {
    width: (width - 48) / 2,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    marginBottom: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  cardIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default Dashboard;