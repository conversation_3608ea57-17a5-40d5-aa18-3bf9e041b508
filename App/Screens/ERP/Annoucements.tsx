import React, {useEffect, useState} from 'react';
import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {PrimaryColors} from '@Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import {getCirculars} from '../../services/studentClassesService';
import Icon from 'react-native-vector-icons/Ionicons';
import {dateFormat} from '../../Utils/Helper';

interface Circular {
  id: number;
  title: string;
  file: string;
  created_at: Date;
}


const AnnoucementsScreen = () => {
  const navigation = useNavigation();
  const {isDarkMode} = IndexStyle();
  const [circular, setCircular] = useState<Circular[]>([]);

  const colors = {
    background: isDarkMode ? '#000000' : PrimaryColors.WHITE,
    cardBackground: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    border: isDarkMode ? '#333' : '#ccc',
    textPrimary: isDarkMode ? '#FFFFFF' : '#000000',
    textSecondary: isDarkMode ? '#AAAAAA' : '#333333',
    shadowColor: isDarkMode ? '#000' : '#000',
  };

  const fetchCirculars = async () => {
    const {data} = await getCirculars();
    console.log(data);
    setCircular(data);
  };

  useEffect(() => {
    fetchCirculars();
  }, []);

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[styles.safeArea, {backgroundColor: colors.background}]}>
        <CurvHeader
          title="Annoucements"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <ScrollView contentContainerStyle={styles.content}>
          {circular.length === 0 ? (
            <Text style={[styles.noDataText, {color: colors.textSecondary}]}>
              No Data Found
            </Text>
          ) : (
            circular.map((item: Circular) => (
              <View style={styles.cardContainer} key={item.id}>
                <View
                  style={[
                    styles.card,
                    {
                      backgroundColor: colors.cardBackground,
                      shadowColor: colors.shadowColor,
                    },
                  ]}>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Title:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {item.title}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Date:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {dateFormat(item.created_at)}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border, alignItems: 'center'},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      File:
                    </Text>
                    <View style={styles.fileRow}>
                      {item.file && (
                        <TouchableOpacity
                          onPress={() => Linking.openURL(item.file)}
                          style={[styles.iconButton, styles.fileRow]}>
                          <Text
                            style={[styles.value, {color: colors.textPrimary}]}
                            numberOfLines={1}>
                            {item.file}
                          </Text>
                          <Icon
                            name="download"
                            size={18}
                            color={colors.textPrimary}
                          />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export {AnnoucementsScreen};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    marginTop: 20,
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 40,
  },
  cardContainer: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
    paddingLeft: 10,
  },
  fileRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
    gap: 8,
  },

  iconButton: {
    padding: 4,
  },
});
