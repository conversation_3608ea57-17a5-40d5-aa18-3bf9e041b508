import React, {useEffect, useState} from 'react';
import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {PrimaryColors} from '@Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import Icon from 'react-native-vector-icons/Ionicons';
import {dateFormat} from '../../Utils/Helper';
import {getDocuments} from '../../services/studentClassesService';

interface Document {
  id: number;
  document_name: string;
  file: string;
  created_at: Date;
  description: string;
  category: {
    category_name: string;
    is_optional: boolean;
  };
}

const DocumentsScreen = () => {
  const navigation = useNavigation();
  const {isDarkMode} = IndexStyle();
  const [document, setDocument] = useState<Document[]>([]);

  const colors = {
    background: isDarkMode ? '#000000' : PrimaryColors.WHITE,
    cardBackground: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    border: isDarkMode ? '#333' : '#ccc',
    textPrimary: isDarkMode ? '#FFFFFF' : '#000000',
    textSecondary: isDarkMode ? '#AAAAAA' : '#333333',
    shadowColor: isDarkMode ? '#000' : '#000',
  };

  const fetchDocuments = async () => {
    const {data} = await getDocuments();
    console.log(data);
    setDocument(data);
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[styles.safeArea, {backgroundColor: colors.background}]}>
        <CurvHeader
          title="Documents"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <ScrollView contentContainerStyle={styles.content}>
          {document.length === 0 ? (
            <Text style={[styles.noDataText, {color: colors.textSecondary}]}>
              No Data Found
            </Text>
          ) : (
            document.map((item: Document) => (
              <View style={styles.cardContainer} key={item.id}>
                <View
                  style={[
                    styles.card,
                    {
                      backgroundColor: colors.cardBackground,
                      shadowColor: colors.shadowColor,
                    },
                  ]}>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Document Name:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {item.document_name}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Date:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {dateFormat(item.created_at)}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Category:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {item?.category?.category_name}
                      {item?.category?.is_optional ? ' (Optional)' : ''}
                    </Text>
                  </View>
                  {item.description && (
                    <View
                      style={[
                        styles.cardRow,
                        {
                          borderBottomColor: colors.border,
                          alignItems: 'center',
                        },
                      ]}>
                      <Text
                        style={[styles.label, {color: colors.textSecondary}]}>
                        Description:
                      </Text>
                      <View style={styles.fileRow}>
                        <Text>{item.description}</Text>
                      </View>
                    </View>
                  )}
                  <View
                    style={[
                      styles.cardRow,
                      {borderBottomColor: colors.border, alignItems: 'center'},
                    ]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      File:
                    </Text>
                    <View style={styles.fileRow}>
                      {item.file && (
                        <TouchableOpacity
                          onPress={() => Linking.openURL(item.file)}
                          style={[styles.iconButton, styles.fileRow]}>
                          <Text
                            style={[styles.value, {color: colors.textPrimary}]}
                            numberOfLines={1}>
                            {item.file}
                          </Text>
                          <Icon
                            name="download"
                            size={18}
                            color={colors.textPrimary}
                          />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export {DocumentsScreen};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    marginTop: 20,
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 40,
  },
  cardContainer: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
    paddingLeft: 10,
  },
  fileRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
    gap: 8,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    backgroundColor: '#FD904B',
    borderRadius: 28,
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 6,
  },
  iconButton: {
    padding: 4,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    color: '#222',
    textAlign: 'center',
  },
});
