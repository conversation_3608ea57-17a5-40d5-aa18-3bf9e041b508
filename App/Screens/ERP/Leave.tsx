import React, { useEffect, useState } from 'react';
import {
  View,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  Text,
  FlatList,
  Modal,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import IndexStyle from '../../Theme/IndexStyle';
import CommonDateTimePicker from '../../CommonComponents/CommonDateTimePicker';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {
  getLeaves,
  applyLeave,
  updateLeave,
  deleteLeave,
} from '../../services/studentClassesService';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Toast from 'react-native-simple-toast';
import { getStatusStyle } from '../../Utils/Helper';
import Badge from '../../CommonComponents/Badge';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const Leave = () => {
  const { styles: themeStyles, isDarkMode } = IndexStyle();
  const navigation = useNavigation();

  const [leaveDate, setLeaveDate] = useState<Date | null>(null);
  const [leaveReason, setLeaveReason] = useState('');
  const [leaves, setLeaves] = useState<any[]>([]);
  const [editId, setEditId] = useState<number | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD');
  };

  const theme = {
    background: isDarkMode ? '#000000' : '#FAFAFA',
    card: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#1A1A1A',
    subText: isDarkMode ? '#AAAAAA' : '#555555',
    accent: '#FD904B',
    border: isDarkMode ? '#333333' : '#E0E0E0',
  };

  const fetchLeaves = async (newPage: number = 1, isRefresh = false) => {
    if (!isRefresh && (loading || !hasMore)) return;
    setLoading(true);
    try {
      const res = await getLeaves(newPage);
      if (newPage === 1) {
        setLeaves(res.data);
      } else {
        setLeaves(prev => [...prev, ...res.data]);
      }

      setPage(res.current_page);
      setHasMore(res.current_page < res.last_page);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaves();
  }, []);

  const handleSubmit = async () => {
    if (!leaveDate || !leaveReason.trim()) return;
    const formattedDate = formatDate(leaveDate);

    try {
      let response: any;
      if (editId) {
        await updateLeave(editId, formattedDate, leaveReason);
        setLeaves(prev =>
          prev.map(leave =>
            leave.id === editId
              ? { ...leave, leave_date: formattedDate, reason: leaveReason }
              : leave,
          ),
        );
        Toast.show('Leave updated successfully', Toast.SHORT);
      } else {
        response = await applyLeave(formattedDate, leaveReason);
        console.log(response);
        setLeaves(prev => [
          { ...response.data, leave_date: formattedDate, reason: leaveReason },
          ...prev,
        ]);
        Toast.show('Leave applied successfully', Toast.SHORT);
      }

      setLeaveDate(null);
      setLeaveReason('');
      setEditId(null);
      setModalVisible(false);
    } catch (err: any) {
      Toast.show(
        err.response?.data?.message || 'Something went wrong',
        Toast.SHORT,
      );
    }
  };

  const handleEdit = (leave: any) => {
    setLeaveDate(new Date(leave.leave_date));
    setLeaveReason(leave.reason);
    setEditId(leave.id);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteLeave(id);
      setLeaves(prev => prev.filter(leave => leave.id !== id));
      Toast.show('Leave deleted successfully', Toast.SHORT);
    } catch (err: any) {
      Toast.show(
        err.response?.data?.message || 'Something went wrong',
        Toast.SHORT,
      );
    }
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      fetchLeaves(page + 1);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchLeaves(1, true); // force fetch without hasMore/loaded check
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const renderLeaveItem = ({ item }: { item: any }) => (
    <View style={[styles.leaveItem, { backgroundColor: theme.card }]}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: 6,
        }}>
        <Text style={{ fontSize: 15, fontWeight: '600', color: theme.text }}>
          {item.reason || 'Leave'}
        </Text>
        <Badge text={item.leave_status} style={[{ fontSize: 13, color: theme.text }]} backgroundColor={getStatusStyle(item.leave_status)} />
      </View>
      <Text style={[styles.leaveText, { color: theme.text }]}>
        Date: {moment(item.leave_date).format('DD MMM YYYY')}
      </Text>

      {item.leave_status !== 'APPROVED' && (
        <View style={styles.actionRow}>
          <TouchableOpacity onPress={() => handleEdit(item)}>
            <MaterialIcons name="edit" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                'Delete Confirmation',
                'Are you sure you want to delete this leave?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => handleDelete(item.id),
                  },
                ]
              );
            }}
          >
            <MaterialIcons name="delete" size={20} color={theme.text} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: theme.background,
        }}>
        <CurvHeader
          title="Leave"
          isBack={true}
          onBackPress={() => navigation.goBack()}
        />

        {leaves.length === 0 ? (
          <Text style={[styles.noData, { color: theme.subText }]}>
            No data found
          </Text>
        ) : (
          <FlatList
            contentContainerStyle={{ padding: 16 }}
            data={leaves}
            keyExtractor={item => item.id.toString()}
            renderItem={renderLeaveItem}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            onEndReached={loadMore}
            onEndReachedThreshold={0.2}
            ListFooterComponent={
              loading ? (
                <Text style={{ textAlign: 'center', marginTop: 10 }}>
                  Loading...
                </Text>
              ) : null
            }
          />
        )}

        <TouchableOpacity
          style={styles.floatingButton}
          onPress={() => setModalVisible(true)}>
          <Ionicons name="add" size={32} color="#fff" />
        </TouchableOpacity>

        <Modal visible={modalVisible} transparent animationType="slide">
          <View style={[styles.modalContainer]}>
            <View style={[styles.formContainer, { backgroundColor: theme.card }]}>
              <Text style={[styles.headerText, { color: theme.text }]}>
                {editId ? 'Edit Leave' : 'Apply Leave'}
              </Text>
              <CommonDateTimePicker
                value={leaveDate}
                innerText={formatDate(leaveDate) || 'Select Date'}
                mode="date"
                onChange={setLeaveDate}
                minimumDate={new Date()}
                style={[styles.datepicker, { color: theme.text }]}

              />
              <TextInput
                style={[styles.textInput, { color: theme.text }]}
                placeholder="Enter reason"
                placeholderTextColor={theme.text}
                value={leaveReason}
                onChangeText={setLeaveReason}
                multiline
              />
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}
                disabled={!leaveDate || !leaveReason.trim()}>
                <Text style={styles.submitButtonText}>
                  {editId ? 'Update' : 'Submit'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(false);
                  setEditId(null);
                  setLeaveDate(null);
                  setLeaveReason('');
                }}>
                <Text
                  style={{ textAlign: 'center', marginTop: 12, color: '#888' }}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Leave;

const styles = StyleSheet.create({
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    elevation: 4,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 2 },
  },
  headerText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
    color: '#222',
    textAlign: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 12,
    padding: 12,
    marginTop: 16,
    minHeight: 100,
    textAlignVertical: 'top',
    fontSize: 14,
    color: '#333',
  },
  datepicker: {
    width: "100%",
  },
  submitButton: {
    marginTop: 20,
    backgroundColor: '#FD904B',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
  listHeader: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 12,
    color: '#222',
    textAlign: 'center',
  },
  leaveItem: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  leaveText: {
    fontSize: 14,
    color: '#444',
    marginBottom: 4,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    gap: 20,
  },
  editText: {
    color: '#1976D2',
    fontWeight: '600',
  },
  deleteText: {
    color: '#E53935',
    fontWeight: '600',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    backgroundColor: '#FD904B',
    borderRadius: 28,
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 6,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  noData: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 40,
  },
});
