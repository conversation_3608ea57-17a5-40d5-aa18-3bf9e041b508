import {StyleSheet, View} from 'react-native';
import React from 'react';
import CalendarStrip from 'react-native-calendar-strip';
import IndexStyle from '../Theme/IndexStyle';
import moment from 'moment';

export default function CommonCalendarStrip({
  selectedDate,
  setSelectedDate,
}: any) {
  const {isDarkMode} = IndexStyle();

  const theme = {
    background: isDarkMode ? '#000000' : '#FAFAFA',
    card: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#1A1A1A',
    subText: isDarkMode ? '#AAAAAA' : '#555555',
    accent: '#FD904B',
    border: isDarkMode ? '#333333' : '#E0E0E0',
  };

  return (
    <View style={[styles.calendarContainer, {borderColor: theme.border}]}>
      <CalendarStrip
        scrollable
        style={styles.calendarStrip}
        calendarHeaderStyle={styles.header}
        dateNumberStyle={[styles.dateNumber, {color: theme.text}]}
        dateNameStyle={[styles.dateName, {color: theme.text}]}
        highlightDateNumberStyle={styles.highlightDateNumber}
        highlightDateNameStyle={styles.highlightDateName}
        selectedDate={moment(selectedDate)}
        onDateSelected={date => setSelectedDate(date.format('YYYY-MM-DD'))}
        minDate={moment('2020-01-01')}
        maxDate={moment('2030-12-31')}
        dayContainerStyle={styles.dayContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  calendarContainer: {
    backgroundColor: 'transparent',
    paddingBottom: 10,
  },
  calendarStrip: {
    height: 100,
    paddingTop: 10,
  },
  header: {
    fontSize: 16,
    fontWeight: 'bold',
    alignSelf: 'center',
    marginBottom: 10,
  },
  dayContainer: {
    paddingVertical: 6,
  },
  dateNumber: {
    fontSize: 16,
  },
  dateName: {
    fontSize: 12,
  },
  highlightDateNumber: {
    color: '#FD904B',
    fontWeight: 'bold',
    fontSize: 18,
  },
  highlightDateName: {
    color: '#FD904B',
    fontWeight: 'bold',
    fontSize: 13,
  },
});
